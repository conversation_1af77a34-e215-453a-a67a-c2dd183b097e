{"version": 3, "sources": ["jquery.pagepiling.js"], "names": ["$", "document", "window", "undefined", "fn", "pagepiling", "custom", "addTableClass", "element", "addClass", "wrapInner", "getYmovement", "destiny", "fromIndex", "index", "toIndex", "scrollPage", "destination", "animated", "v", "activeSection", "anchorLink", "data", "sectionIndex", "to<PERSON><PERSON>", "yMovement", "leavingSection", "is", "setURLHash", "siblings", "removeClass", "sectionsToMove", "getSectionsToMove", "translate3d", "getTranslate3d", "scrolling", "options", "css3", "each", "this", "css", "getScrollProp", "animateSection", "isFunction", "onLeave", "call", "performMovement", "activateMenuElement", "activateNavDots", "lastScrolledDestiny", "timeNow", "Date", "getTime", "lastAnimation", "transformContainer", "setTimeout", "afterSectionLoads", "scrollingSpeed", "scrollOptions", "animate", "easing", "readjustSections", "afterLoad", "sectionToMove", "map", "propertyValue", "direction", "top", "left", "anchors", "length", "location", "hash", "setBodyClass", "String", "text", "replace", "className", "scrollToAnchor", "value", "sectionAnchor", "section", "find", "animateAnchor", "isMoving", "scrollDelay", "hash<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "isNaN", "eq", "getTransforms", "-webkit-transform", "-moz-transform", "-ms-transform", "transform", "toggleClass", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "curTime", "event", "wheelDelta", "deltaY", "detail", "delta", "Math", "max", "min", "horizontalDetection", "wheelDeltaX", "deltaX", "isScrollingVertically", "abs", "scrollings", "shift", "push", "timeDiff", "prevTime", "scrollable", "isScrollable", "averageEnd", "getAverage", "averageMiddle", "isAccelerating", "elements", "number", "sum", "lastElements", "slice", "i", "ceil", "type", "check", "scrollSection", "PP", "moveSectionDown", "moveSectionUp", "isScrolled", "scrollTop", "innerHeight", "scrollHeight", "filter", "removeMouseWheelHandler", "container", "get", "addEventListener", "removeEventListener", "detachEvent", "addMouseWheelHandler", "attachEvent", "addTouchHandler", "is<PERSON><PERSON>ch", "MSPointer", "getMS<PERSON>ointer", "off", "down", "on", "touchStartHandler", "move", "touchMoveHandler", "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointer", "PointerEvent", "up", "getEventsPage", "events", "Array", "y", "pageY", "pageX", "touches", "x", "is<PERSON><PERSON>ly<PERSON><PERSON><PERSON>", "pointerType", "originalEvent", "touchEvents", "touchStartY", "touchStartX", "checkParentForNormalScrollElement", "target", "preventDefault", "touchEndY", "touchEndX", "width", "touchSensitivity", "height", "el", "hop", "parent", "normalScrollElementTouchThreshold", "normalScrollElements", "addVerticalNavigation", "append", "nav", "navigation", "textColor", "position", "cont", "link", "tooltips", "tooltip", "bulletsColor", "name", "menu", "support3d", "has3d", "createElement", "transforms", "webkitTransform", "OTransform", "msTransform", "MozTransform", "body", "insertBefore", "t", "style", "getComputedStyle", "getPropertyValue", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "msMaxTouchPoints", "extend", "verticalCentered", "sectionsColor", "loopBottom", "loopTop", "keyboardScrolling", "sectionSelector", "afterRender", "easeInQuart", "b", "c", "d", "setScrollingSpeed", "setMouseWheelScrolling", "setAllowScrolling", "setKeyboardScrolling", "prev", "last", "next", "first", "moveTo", "overflow", "-ms-touch-action", "touch-action", "isEmptyObject", "zIndex", "attr", "hasClass", "promise", "done", "keydown", "which", "mouseenter", "hide", "appendTo", "fadeIn", "mouseleave", "fadeOut", "remove", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;CAQA,SAAWA,EAAGC,EAAUC,EAAQC,GAC5B,YAEAH,GAAEI,GAAGC,WAAa,SAAUC,GA0MxB,QAASC,GAAcC,GACnBA,EAAQC,SAAS,YAAYC,UAAU,oDAQ3C,QAASC,GAAaC,GAClB,GAAIC,GAAYb,EAAE,sBAAsBc,MAAM,eAC1CC,EAAUH,EAAQE,MAAM,cAE5B,OAAGD,GAAYE,EACJ,KAEJ,OAMX,QAASC,GAAWC,EAAaC,GAC7B,GAAIC,IACAF,YAAaA,EACbC,SAAUA,EACVE,cAAepB,EAAE,sBACjBqB,WAAYJ,EAAYK,KAAK,UAC7BC,aAAcN,EAAYH,MAAM,eAChCU,OAAQP,EACRQ,UAAWd,EAAaM,GACxBS,eAAgB1B,EAAE,sBAAsBc,MAAM,eAAiB,EAInE,KAAGK,EAAEC,cAAcO,GAAGV,GAAtB,CAEyB,mBAAfE,GAAED,WACRC,EAAED,UAAW,GAGU,mBAAjBC,GAAEE,YACRO,EAAWT,EAAEE,WAAYF,EAAEI,cAG/BJ,EAAEF,YAAYR,SAAS,UAAUoB,WAAWC,YAAY,UAExDX,EAAEY,eAAiBC,EAAkBb,GAGjB,SAAhBA,EAAEM,WACFN,EAAEc,YAAcC,IAChBf,EAAEgB,UAAY,QAEVC,EAAQC,MACRlB,EAAEY,eAAeO,KAAK,SAASxB,GACxBA,GAASK,EAAEC,cAAcN,MAAM,gBAC9Bd,EAAEuC,MAAMC,IAAIC,EAActB,EAAEgB,cAKxChB,EAAEuB,eAAiBvB,EAAEC,gBAKrBD,EAAEc,YAAc,6BAChBd,EAAEgB,UAAY,IAEdhB,EAAEuB,eAAiBzB,GAGvBjB,EAAE2C,WAAWP,EAAQQ,UAAYR,EAAQQ,QAAQC,KAAKN,KAAMpB,EAAEO,eAAiBP,EAAEI,aAAe,EAAIJ,EAAEM,WAEtGqB,EAAgB3B,GAEhB4B,EAAoB5B,EAAEE,YACtB2B,EAAgB7B,EAAEE,WAAYF,EAAEI,cAChC0B,EAAsB9B,EAAEE,UAExB,IAAI6B,IAAU,GAAIC,OAAOC,SACzBC,GAAgBH,GAMpB,QAASJ,GAAgB3B,GAClBiB,EAAQC,MACPiB,EAAmBnC,EAAEuB,eAAgBvB,EAAEc,YAAad,EAAED,UAEtDC,EAAEY,eAAeO,KAAK,WAClBgB,EAAmBtD,EAAEuC,MAAOpB,EAAEc,YAAad,EAAED,YAGjDqC,WAAW,WACPC,EAAkBrC,IACnBiB,EAAQqB,kBAEXtC,EAAEuC,cAAgBjB,EAActB,EAAEgB,WAE/BhB,EAAED,SACDC,EAAEuB,eAAeiB,QACbxC,EAAEuC,cACNtB,EAAQqB,eAAgBrB,EAAQwB,OAAQ,WACpCC,EAAiB1C,GACjBqC,EAAkBrC,MAGtBA,EAAEuB,eAAeF,IAAIC,EAActB,EAAEgB,YACrCoB,WAAW,WACPM,EAAiB1C,GACjBqC,EAAkBrC,IACpB,OAQd,QAASqC,GAAkBrC,GAEvBnB,EAAE2C,WAAWP,EAAQ0B,YAAc1B,EAAQ0B,UAAUjB,KAAKN,KAAMpB,EAAEE,WAAaF,EAAEI,aAAe,GAIpG,QAASS,GAAkBb,GACvB,GAAI4C,EAgBJ,OAbIA,GADe,SAAhB5C,EAAEM,UACezB,EAAE,eAAegE,IAAI,SAASlD,GAC1C,GAAIA,EAAQK,EAAEF,YAAYH,MAAM,eAC5B,MAAOd,GAAEuC,QAIDvC,EAAE,eAAegE,IAAI,SAASlD,GAC1C,GAAIA,EAAQK,EAAEF,YAAYH,MAAM,eAC5B,MAAOd,GAAEuC,QAWzB,QAASsB,GAAiB1C,GACH,OAAhBA,EAAEM,WACDN,EAAEY,eAAeO,KAAK,SAASxB,GAC3Bd,EAAEuC,MAAMC,IAAIC,EAActB,EAAEgB,cASxC,QAASM,GAAcwB,GACnB,MAAyB,aAAtB7B,EAAQ8B,WACCC,IAAOF,IAEXG,KAAQH,GAkBpB,QAASrC,GAAWP,EAAYE,GACzBa,EAAQiC,QAAQC,QACfC,SAASC,KAAOnD,EAEhBoD,EAAaF,SAASC,OAEtBC,EAAaC,OAAOnD,IAO5B,QAASkD,GAAaE,GAElBA,EAAOA,EAAKC,QAAQ,IAAI,IAGxB5E,EAAE,QAAQ,GAAG6E,UAAY7E,EAAE,QAAQ,GAAG6E,UAAUD,QAAQ,4BAA6B,IAGrF5E,EAAE,QAAQS,SAAS,cAAgBkE,GAIvC,QAASG,KAEL,GAAIC,GAAS7E,EAAOqE,SAASC,KAAKI,QAAQ,IAAK,IAC3CI,EAAgBD,EAChBE,EAAUjF,EAAEC,GAAUiF,KAAK,4BAA4BF,EAAc,KAEtEC,GAAQX,OAAS,GAChBtD,EAAWiE,EAAS7C,EAAQ+C,eAQpC,QAASC,KACL,GAAIlC,IAAU,GAAIC,OAAOC,SAEzB,OAAIF,GAAUG,EAAgBgC,EAAcjD,EAAQqB,eAaxD,QAAS6B,KACL,GAAIP,GAAS7E,EAAOqE,SAASC,KAAKI,QAAQ,IAAK,IAAIW,MAAM,KACrDP,EAAgBD,EAAM,EAE1B,IAAGC,EAAcV,QAITU,GAAiBA,IAAkB/B,EAAsB,CACzD,GAAIgC,EAGAA,GADDO,MAAMR,GACKhF,EAAEC,GAAUiF,KAAK,iBAAiBF,EAAc,MAEhDhF,EAAE,eAAeyF,GAAKT,EAAe,GAEnDhE,EAAWiE,IAQvB,QAASS,GAAczD,GACnB,OACI0D,oBAAqB1D,EACjB2D,iBAAkB3D,EAClB4D,gBAAiB5D,EACjB6D,UAAa7D,GAOzB,QAASqB,GAAmB9C,EAASyB,EAAaf,GAC9CV,EAAQuF,YAAY,YAAa7E,GAEjCV,EAAQgC,IAAIkD,EAAczD,IAsE9B,QAAS+D,GAAkBC,GAC1B,GAAIC,IAAU,GAAI/C,OAAOC,SAGtB6C,GAAIA,GAAK/F,EAAOiG,KAChB,IAAIpB,GAAQkB,EAAEG,aAAeH,EAAEI,SAAWJ,EAAEK,OACxCC,EAAQC,KAAKC,OAAQD,KAAKE,IAAI,EAAG3B,IAEjC4B,EAA+C,mBAAlBV,GAAEW,aAAmD,mBAAbX,GAAEY,OACvEC,EAAyBN,KAAKO,IAAId,EAAEW,aAAeJ,KAAKO,IAAId,EAAEG,aAAiBI,KAAKO,IAAId,EAAEY,QAAWL,KAAKO,IAAId,EAAEI,UAAYM,CAG7HK,GAAW1C,OAAS,KACnB0C,EAAWC,QAIfD,EAAWE,KAAKV,KAAKO,IAAIhC,GAGzB,IAAIoC,GAAWjB,EAAQkB,CAUvB,IATAA,EAAWlB,EAIRiB,EAAW,MAEVH,OAGA5B,IAAW,CACX,GAAIhE,GAAgBpB,EAAE,sBAClBqH,EAAaC,EAAalG,GAE1BmG,EAAaC,EAAWR,EAAY,IACpCS,EAAgBD,EAAWR,EAAY,IACvCU,EAAiBH,GAAcE,CAanC,OAXGC,IAAkBZ,IAEhBP,EAAQ,EACRpE,EAAU,OAAQkF,GAGbd,EAAM,GACXpE,EAAU,KAAMkF,KAId,GAOf,QAASG,GAAWG,EAAUC,GAM1B,IAAI,GALAC,GAAM,EAGNC,EAAeH,EAASI,MAAMvB,KAAKC,IAAIkB,EAASrD,OAASsD,EAAQ,IAE7DI,EAAI,EAAGA,EAAIF,EAAaxD,OAAQ0D,IACpCH,GAAYC,EAAaE,EAG7B,OAAOxB,MAAKyB,KAAKJ,EAAID,GAOzB,QAASzF,GAAU+F,EAAMb,GACrB,GAAIc,GACAC,CAUJ,IARW,QAARF,GACCC,EAAQ,SACRC,EAAgBC,EAAGC,kBAEnBH,EAAQ,MACRC,EAAgBC,EAAGE,eAGpBlB,EAAW/C,OAAS,EAAG,CAEtB,IAAGkE,EAAWL,EAAOd,GAGjB,OAAO,CAFPe,SAMJA,KAQR,QAASI,GAAWN,EAAMb,GACtB,MAAY,QAATa,GACSb,EAAWoB,YACL,WAATP,EACEb,EAAWoB,YAAc,EAAIpB,EAAWqB,eAAiBrB,EAAW,GAAGsB,aAD5E,OAQV,QAASrB,GAAalG,GAClB,MAAOA,GAAcwH,OAAO,kBAOhC,QAASC,KACDC,EAAUC,IAAI,GAAGC,kBACjBF,EAAUC,IAAI,GAAGE,oBAAoB,aAAcjD,GAAmB,GACtE8C,EAAUC,IAAI,GAAGE,oBAAoB,QAASjD,GAAmB,IAEjE8C,EAAUC,IAAI,GAAGG,YAAY,eAAgBlD,GAQrD,QAASmD,KACDL,EAAUxE,SACNwE,EAAUC,IAAI,GAAGC,kBACjBF,EAAUC,IAAI,GAAGC,iBAAiB,aAAchD,GAAmB,GACnE8C,EAAUC,IAAI,GAAGC,iBAAiB,QAAShD,GAAmB,IAE9D8C,EAAUC,IAAI,GAAGK,YAAY,eAAgBpD,IAQzD,QAASqD,KACL,GAAGC,EAAQ,CAEP,GAAIC,GAAYC,GAEhBV,GAAUW,IAAI,cAAiBF,EAAUG,MAAMC,GAAG,cAAgBJ,EAAUG,KAAME,GAClFd,EAAUW,IAAI,aAAeF,EAAUM,MAAMF,GAAG,aAAeJ,EAAUM,KAAMC,IAOvF,QAASC,KACL,GAAGT,EAAQ,CAEP,GAAIC,GAAYC,GAEhBV,GAAUW,IAAI,cAAgBF,EAAUG,MACxCZ,EAAUW,IAAI,aAAeF,EAAUM,OAQ/C,QAASL,KACL,GAAIQ,EAYJ,OARIA,GADD9J,EAAO+J,cACMP,KAAM,cAAeG,KAAM,cAAeK,GAAI,cAK9CR,KAAM,gBAAiBG,KAAM,gBAAiBK,GAAI,eAUtE,QAASC,GAAclE,GACnB,GAAImE,GAAS,GAAIC,MAKjB,OAHAD,GAAOE,EAAwB,mBAAZrE,GAAEsE,QAA0BtE,EAAEsE,OAAStE,EAAEuE,OAASvE,EAAEsE,MAAQtE,EAAEwE,QAAQ,GAAGF,MAC5FH,EAAOM,EAAwB,mBAAZzE,GAAEuE,QAA0BvE,EAAEsE,OAAStE,EAAEuE,OAASvE,EAAEuE,MAAQvE,EAAEwE,QAAQ,GAAGD,MAErFJ,EAOX,QAASO,GAAc1E,GAEnB,MAAgC,mBAAlBA,GAAE2E,aAAgD,SAAjB3E,EAAE2E,YAMrD,QAAShB,GAAkBzD,GACvB,GAAIF,GAAIE,EAAM0E,aAEd,IAAGF,EAAc1E,GAAG,CAChB,GAAI6E,GAAcX,EAAclE,EAChC8E,GAAcD,EAAYR,EAC1BU,EAAcF,EAAYJ,GAMlC,QAASZ,GAAiB3D,GACtB,GAAIF,GAAIE,EAAM0E,aAGd,KAAMI,EAAkC9E,EAAM+E,SAAWP,EAAc1E,GAAK,CAExE,GAAI7E,GAAgBpB,EAAE,sBAClBqH,EAAaC,EAAalG,EAM9B,IAJIiG,EAAW/C,QACX6B,EAAMgF,kBAGL/F,IAAY,CACb,GAAI0F,GAAcX,EAAclE,EAChCmF,GAAYN,EAAYR,EACxBe,EAAYP,EAAYJ,EAKE,eAAtBtI,EAAQ8B,WAA8BsC,KAAKO,IAAIiE,EAAcK,GAAc7E,KAAKO,IAAIgE,EAAcK,GAE9F5E,KAAKO,IAAIiE,EAAcK,GAAcvC,EAAUwC,QAAU,IAAMlJ,EAAQmJ,mBACnEP,EAAcK,EACdlJ,EAAU,OAAQkF,GACXgE,EAAYL,GACnB7I,EAAU,KAAMkF,IAIpBb,KAAKO,IAAIgE,EAAcK,GAActC,EAAU0C,SAAW,IAAMpJ,EAAQmJ,mBACpER,EAAcK,EACdjJ,EAAU,OAAQkF,GACX+D,EAAYL,GACnB5I,EAAU,KAAMkF,MAexC,QAAS4D,GAAmCQ,EAAIC,GAC5CA,EAAMA,GAAO,CACb,IAAIC,GAAS3L,EAAEyL,GAAIE,QAEnB,UAAID,EAAMtJ,EAAQwJ,mCACdD,EAAOhK,GAAGS,EAAQyJ,wBAEXH,GAAOtJ,EAAQwJ,mCAGfX,EAAkCU,IAAUD,GAQ3D,QAASI,KACL9L,EAAE,QAAQ+L,OAAO,mCACjB,IAAIC,GAAMhM,EAAE,UAEZgM,GAAIxJ,IAAI,QAASJ,EAAQ6J,WAAWC,WAEpCF,EAAIvL,SAAS2B,EAAQ6J,WAAWE,SAEhC,KAAI,GAAIC,GAAO,EAAGA,EAAOpM,EAAE,eAAesE,OAAQ8H,IAAO,CACrD,GAAIC,GAAO,EAIX,IAHGjK,EAAQiC,QAAQC,SACf+H,EAAOjK,EAAQiC,QAAQ+H,IAEQ,cAAhChK,EAAQ6J,WAAWK,SAAyB,CAC3C,GAAIC,GAAUnK,EAAQ6J,WAAWK,SAASF,EACpB,oBAAZG,KACNA,EAAU,IAIlBP,EAAI9G,KAAK,MAAM6G,OAAO,qBAAuBQ,EAAU,eAAiBF,EAAO,4BAGnFL,EAAI9G,KAAK,QAAQ1C,IAAI,eAAgBJ,EAAQ6J,WAAWO,cA+B5D,QAASxJ,GAAgByJ,EAAMlL,GACxBa,EAAQ6J,aACPjM,EAAE,WAAWkF,KAAK,WAAWpD,YAAY,UACtC2K,EACCzM,EAAE,WAAWkF,KAAK,YAAcuH,EAAO,MAAMhM,SAAS,UAEtDT,EAAE,WAAWkF,KAAK,MAAMO,GAAGlE,GAAc2D,KAAK,KAAKzE,SAAS,WAQxE,QAASsC,GAAoB0J,GACtBrK,EAAQsK,OACP1M,EAAEoC,EAAQsK,MAAMxH,KAAK,WAAWpD,YAAY,UAC5C9B,EAAEoC,EAAQsK,MAAMxH,KAAK,qBAAqBuH,EAAK,MAAMhM,SAAS,WAStE,QAASkM,KACL,GACIC,GADAnB,EAAKxL,EAAS4M,cAAc,KAE5BC,GACIC,gBAAkB,oBAClBC,WAAa,eACbC,YAAc,gBACdC,aAAe,iBACfpH,UAAY,YAIpB7F,GAASkN,KAAKC,aAAa3B,EAAI,KAE/B,KAAK,GAAI4B,KAAKP,GACNrB,EAAG6B,MAAMD,KAAOlN,IAChBsL,EAAG6B,MAAMD,GAAK,2BACdT,EAAQ1M,EAAOqN,iBAAiB9B,GAAI+B,iBAAiBV,EAAWO,IAMxE,OAFApN,GAASkN,KAAKM,YAAYhC,GAElBmB,IAAUzM,GAAayM,EAAMtI,OAAS,GAAe,SAAVsI,EAMvD,QAAS1K,KACL,MAA0B,aAAtBE,EAAQ8B,UACC,+BAGN,+BAj8BX,GAEIjB,GAFAoF,EAAKrI,EAAEI,GAAGC,WACVyI,EAAY9I,EAAEuC,MAEdc,EAAgB,EAChBiG,EAAY,gBAAkBpJ,IAAYwN,UAAUC,iBAAmB,GAAOD,UAAwB,eACtG3C,EAAc,EAAGC,EAAc,EAAGI,EAAY,EAAGC,EAAY,EAC7DrE,KAKA3B,EAAc,IAGdjD,EAAUpC,EAAE4N,QAAO,GACnB1J,UAAW,WACXwI,KAAM,KACNmB,kBAAkB,EAClBC,iBACAzJ,WACAZ,eAAgB,IAChBG,OAAQ,cACRmK,YAAY,EACZC,SAAS,EACT3L,MAAM,EACN4J,YACIC,UAAW,OACXM,aAAc,OACdL,SAAU,QACVG,aAEJT,qBAAsB,KACtBD,kCAAmC,EACnCL,iBAAkB,EAClB0C,mBAAmB,EACnBC,gBAAiB,WACjB/I,eAAe,EAGfrB,UAAW,KACXlB,QAAS,KACTuL,YAAa,MACd7N,EAIHN,GAAE4N,OAAO5N,EAAE4D,QAASwK,YAAa,SAAU1D,EAAG2C,EAAGgB,EAAGC,EAAGC,GAAK,MAAOD,IAAGjB,GAAGkB,GAAGlB,EAAEA,EAAEA,EAAIgB,KAKpFhG,EAAGmG,kBAAoB,SAASzJ,GAC7B3C,EAAQqB,eAAiBsB,GAM5BsD,EAAGoG,uBAAyB,SAAU1J,GAC/BA,EACCoE,IAEAN,KAORR,EAAGqG,kBAAoB,SAAU3J,GAC1BA,GACCsD,EAAGoG,wBAAuB,GAC1BpF,MAEAhB,EAAGoG,wBAAuB,GAC1B1E,MAOR1B,EAAGsG,qBAAuB,SAAU5J,GAChC3C,EAAQ6L,kBAAoBlJ,GAMhCsD,EAAGE,cAAgB,WACf,GAAIqG,GAAO5O,EAAE,sBAAsB4O,KAAK,gBAGnCA,EAAKtK,QAAUlC,EAAQ4L,UACxBY,EAAO5O,EAAE,eAAe6O,QAGxBD,EAAKtK,QACLtD,EAAW4N,IAOnBvG,EAAGC,gBAAkB,WACjB,GAAIwG,GAAO9O,EAAE,sBAAsB8O,KAAK,gBAGpCA,EAAKxK,QAAUlC,EAAQ2L,aACvBe,EAAO9O,EAAE,eAAe+O,SAGxBD,EAAKxK,QACLtD,EAAW8N,IAOnBzG,EAAG2G,OAAS,SAAU/J,GAClB,GAAIrE,GAAU,EAGVA,GADD4E,MAAMP,GACKjF,EAAEC,GAAUiF,KAAK,iBAAiBD,EAAQ,MAE1CjF,EAAE,eAAeyF,GAAKR,EAAS,GAI1CrE,EAAQ0D,OAAS,GAChBtD,EAAWJ,IAKnBZ,EAAEoC,EAAQ8L,iBAAiB5L,KAAK,WAC5BtC,EAAEuC,MAAM9B,SAAS,gBAIlB2B,EAAQC,OACPD,EAAQC,KAAOsK,KAGnB3M,EAAE8I,GAAWtG,KACTyM,SAAa,SACbC,mBAAoB,OACpBC,eAAgB,SAIpB9G,EAAGqG,mBAAkB,GAGhB1O,EAAEoP,cAAchN,EAAQ6J,aACzBH,GAGH,IAAIuD,GAASrP,EAAE,eAAesE,MAE/BtE,GAAE,eAAesC,KAAK,SAAUxB,GAC5Bd,EAAEuC,MAAMjB,KAAK,aAAcR,GAC3Bd,EAAEuC,MAAMC,IAAI,UAAW6M,GAGlBvO,GAA4C,IAAnCd,EAAE,sBAAsBsE,QAClCtE,EAAEuC,MAAM9B,SAAS,UAGiB,mBAA3B2B,GAAQiC,QAAQvD,IACvBd,EAAEuC,MAAM+M,KAAK,cAAelN,EAAQiC,QAAQvD,IAGJ,mBAAjCsB,GAAQ0L,cAAchN,IAC7Bd,EAAEuC,MAAMC,IAAI,mBAAoBJ,EAAQ0L,cAAchN,IAGvDsB,EAAQyL,mBAAqB7N,EAAEuC,MAAMgN,SAAS,kBAC7ChP,EAAcP,EAAEuC,OAGpB8M,GAAkB,IACnBG,UAAUC,KAAK,WAEXrN,EAAQ6J,aACPjM,EAAE,WAAWwC,IAAI,aAAc,IAAOxC,EAAE,WAAWwL,SAAS,EAAK,MACjExL,EAAE,WAAWkF,KAAK,MAAMO,GAAGzF,EAAE,sBAAsBc,MAAM,gBAAgBoE,KAAK,KAAKzE,SAAS,WAGhGT,EAAEE,GAAQyJ,GAAG,OAAQ,WACjB7E,MAGJ9E,EAAE2C,WAAYP,EAAQ+L,cAAiB/L,EAAQ+L,YAAYtL,KAAMN,QAkPrEvC,EAAEE,GAAQyJ,GAAG,aAAcrE,GAkD3BtF,EAAEC,GAAUyP,QAAQ,SAAUzJ,GAC1B,GAAG7D,EAAQ6L,oBAAsB7I,IAE7B,OAAQa,EAAE0J,OAEN,IAAK,IACL,IAAK,IACDtH,EAAGE,eACH,MAGJ,KAAK,IACL,IAAK,IACDF,EAAGC,iBACH,MAGJ,KAAK,IACDD,EAAG2G,OAAO,EACV,MAGJ,KAAK,IACD3G,EAAG2G,OAAOhP,EAAE,eAAesE,OAC3B,MAGJ,KAAK,IACD+D,EAAGE,eACH,MAGJ,KAAK,IACDF,EAAGC,iBACH,MAEJ,SACI,UASblG,EAAQyJ,uBACP7L,EAAEC,GAAU0J,GAAG,aAAcvH,EAAQyJ,qBAAsB,WACvDxD,EAAGoG,wBAAuB,KAG9BzO,EAAEC,GAAU0J,GAAG,aAAcvH,EAAQyJ,qBAAsB,WACvDxD,EAAGoG,wBAAuB,KAUlC,IAAIrH,IAAW,GAAIjE,OAAOC,SAuU1BpD,GAAEC,GAAU0J,GAAG,mBAAoB,YAAa,SAAS1D,GACrDA,EAAEkF,gBACF,IAAIrK,GAAQd,EAAEuC,MAAMoJ,SAAS7K,OAE7BE,GAAWhB,EAAE,eAAeyF,GAAG3E,MAMnCd,EAAEC,GAAU0J,IACRiG,WAAY,WACR,GAAIrD,GAAUvM,EAAEuC,MAAMjB,KAAK,UAC3BtB,GAAE,0BAA4BoC,EAAQ6J,WAAWE,SAAU,KAAOI,EAAU,UAAUsD,OAAOC,SAAS9P,EAAEuC,OAAOwN,OAAO,MAE1HC,WAAY,WACRhQ,EAAEuC,MAAM2C,KAAK,eAAe+K,QAAQ,IAAK,WACrCjQ,EAAEuC,MAAM2N,aAGjB,gBAqERC,OAAQlQ,SAAUC", "file": "jquery.pagepiling.min.js", "sourcesContent": ["/*!\r\n * pagepiling.js 1.5.6\r\n *\r\n * https://github.com/alvarotrigo/pagePiling.js\r\n * @license MIT licensed\r\n *\r\n * Copyright (C) 2016 alvarotrigo.com - A project by <PERSON><PERSON>\r\n */\r\n(function ($, document, window, undefined) {\r\n    'use strict';\r\n\r\n    $.fn.pagepiling = function (custom) {\r\n        var PP = $.fn.pagepiling;\r\n        var container = $(this);\r\n        var lastScrolledDestiny;\r\n        var lastAnimation = 0;\r\n        var isTouch = (('ontouchstart' in window) || (navigator.msMaxTouchPoints > 0) || (navigator.maxTouchPoints));\r\n        var touchStartY = 0, touchStartX = 0, touchEndY = 0, touchEndX = 0;\r\n        var scrollings = [];\r\n\r\n        //Defines the delay to take place before being able to scroll to the next section\r\n        //BE CAREFUL! Not recommened to change it under 400 for a good behavior in laptops and\r\n        //Apple devices (laptops, mouses...)\r\n        var scrollDelay = 600;\r\n\r\n        // Create some defaults, extending them with any options that were provided\r\n        var options = $.extend(true, {\r\n            direction: 'vertical',\r\n            menu: null,\r\n            verticalCentered: true,\r\n            sectionsColor: [],\r\n            anchors: [],\r\n            scrollingSpeed: 700,\r\n            easing: 'easeInQuart',\r\n            loopBottom: false,\r\n            loopTop: false,\r\n            css3: true,\r\n            navigation: {\r\n                textColor: '#000',\r\n                bulletsColor: '#000',\r\n                position: 'right',\r\n                tooltips: []\r\n            },\r\n            normalScrollElements: null,\r\n            normalScrollElementTouchThreshold: 5,\r\n            touchSensitivity: 5,\r\n            keyboardScrolling: true,\r\n            sectionSelector: '.section',\r\n            animateAnchor: false,\r\n\r\n            //events\r\n            afterLoad: null,\r\n            onLeave: null,\r\n            afterRender: null\r\n        }, custom);\r\n\r\n\r\n        //easeInQuart animation included in the plugin\r\n        $.extend($.easing,{ easeInQuart: function (x, t, b, c, d) { return c*(t/=d)*t*t*t + b; }});\r\n\r\n        /**\r\n        * Defines the scrolling speed\r\n        */\r\n        PP.setScrollingSpeed = function(value){\r\n           options.scrollingSpeed = value;\r\n        };\r\n\r\n        /**\r\n        * Adds or remove the possiblity of scrolling through sections by using the mouse wheel or the trackpad.\r\n        */\r\n        PP.setMouseWheelScrolling = function (value){\r\n            if(value){\r\n                addMouseWheelHandler();\r\n            }else{\r\n                removeMouseWheelHandler();\r\n            }\r\n        };\r\n\r\n        /**\r\n        * Adds or remove the possiblity of scrolling through sections by using the mouse wheel/trackpad or touch gestures.\r\n        */\r\n        PP.setAllowScrolling = function (value){\r\n            if(value){\r\n                PP.setMouseWheelScrolling(true);\r\n                addTouchHandler();\r\n            }else{\r\n                PP.setMouseWheelScrolling(false);\r\n                removeTouchHandler();\r\n            }\r\n        };\r\n\r\n        /**\r\n        * Adds or remove the possiblity of scrolling through sections by using the keyboard arrow keys\r\n        */\r\n        PP.setKeyboardScrolling = function (value){\r\n            options.keyboardScrolling = value;\r\n        };\r\n\r\n        /**\r\n        * Moves sectio up\r\n        */\r\n        PP.moveSectionUp = function () {\r\n            var prev = $('.pp-section.active').prev('.pp-section');\r\n\r\n            //looping to the bottom if there's no more sections above\r\n            if (!prev.length && options.loopTop) {\r\n                prev = $('.pp-section').last();\r\n            }\r\n\r\n            if (prev.length) {\r\n                scrollPage(prev);\r\n            }\r\n        };\r\n\r\n        /**\r\n        * Moves sectio down\r\n        */\r\n        PP.moveSectionDown = function () {\r\n            var next = $('.pp-section.active').next('.pp-section');\r\n\r\n            //looping to the top if there's no more sections below\r\n            if(!next.length && options.loopBottom){\r\n                next = $('.pp-section').first();\r\n            }\r\n\r\n            if (next.length) {\r\n                scrollPage(next);\r\n            }\r\n        };\r\n\r\n        /**\r\n        * Moves the site to the given anchor or index\r\n        */\r\n        PP.moveTo = function (section){\r\n            var destiny = '';\r\n\r\n            if(isNaN(section)){\r\n                destiny = $(document).find('[data-anchor=\"'+section+'\"]');\r\n            }else{\r\n                destiny = $('.pp-section').eq( (section -1) );\r\n            }\r\n\r\n\r\n            if(destiny.length > 0){\r\n                scrollPage(destiny);\r\n            }\r\n        };\r\n\r\n        //adding internal class names to void problem with common ones\r\n        $(options.sectionSelector).each(function(){\r\n            $(this).addClass('pp-section');\r\n        });\r\n\r\n        //if css3 is not supported, it will use jQuery animations\r\n        if(options.css3){\r\n            options.css3 = support3d();\r\n        }\r\n\r\n        $(container).css({\r\n            'overflow' : 'hidden',\r\n            '-ms-touch-action': 'none',  /* Touch detection for Windows 8 */\r\n            'touch-action': 'none'       /* IE 11 on Windows Phone 8.1*/\r\n        });\r\n\r\n        //init\r\n        PP.setAllowScrolling(true);\r\n\r\n        //creating the navigation dots\r\n        if (!$.isEmptyObject(options.navigation) ) {\r\n            addVerticalNavigation();\r\n        }\r\n\r\n         var zIndex = $('.pp-section').length;\r\n\r\n        $('.pp-section').each(function (index) {\r\n            $(this).data('data-index', index);\r\n            $(this).css('z-index', zIndex);\r\n\r\n            //if no active section is defined, the 1st one will be the default one\r\n            if (!index && $('.pp-section.active').length === 0) {\r\n                $(this).addClass('active');\r\n            }\r\n\r\n            if (typeof options.anchors[index] !== 'undefined') {\r\n                $(this).attr('data-anchor', options.anchors[index]);\r\n            }\r\n\r\n            if (typeof options.sectionsColor[index] !== 'undefined') {\r\n                $(this).css('background-color', options.sectionsColor[index]);\r\n            }\r\n\r\n            if(options.verticalCentered && !$(this).hasClass('pp-scrollable')){\r\n                addTableClass($(this));\r\n            }\r\n\r\n            zIndex = zIndex - 1;\r\n        }).promise().done(function(){\r\n            //vertical centered of the navigation + first bullet active\r\n            if(options.navigation){\r\n                $('#pp-nav').css('margin-top', '-' + ($('#pp-nav').height()/2) + 'px');\r\n                $('#pp-nav').find('li').eq($('.pp-section.active').index('.pp-section')).find('a').addClass('active');\r\n            }\r\n\r\n            $(window).on('load', function() {\r\n                scrollToAnchor();\r\n            });\r\n\r\n            $.isFunction( options.afterRender ) && options.afterRender.call( this);\r\n        });\r\n\r\n        /**\r\n        * Enables vertical centering by wrapping the content and the use of table and table-cell\r\n        */\r\n        function addTableClass(element){\r\n            element.addClass('pp-table').wrapInner('<div class=\"pp-tableCell\" style=\"height:100%\" />');\r\n        }\r\n\r\n\r\n       /**\r\n        * Retuns `up` or `down` depending on the scrolling movement to reach its destination\r\n        * from the current section.\r\n        */\r\n        function getYmovement(destiny){\r\n            var fromIndex = $('.pp-section.active').index('.pp-section');\r\n            var toIndex = destiny.index('.pp-section');\r\n\r\n            if(fromIndex > toIndex){\r\n                return 'up';\r\n            }\r\n            return 'down';\r\n        }\r\n\r\n        /**\r\n        * Scrolls the page to the given destination\r\n        */\r\n        function scrollPage(destination, animated) {\r\n            var v ={\r\n                destination: destination,\r\n                animated: animated,\r\n                activeSection: $('.pp-section.active'),\r\n                anchorLink: destination.data('anchor'),\r\n                sectionIndex: destination.index('.pp-section'),\r\n                toMove: destination,\r\n                yMovement: getYmovement(destination),\r\n                leavingSection: $('.pp-section.active').index('.pp-section') + 1\r\n            };\r\n\r\n            //quiting when activeSection is the target element\r\n            if(v.activeSection.is(destination)){ return; }\r\n\r\n            if(typeof v.animated === 'undefined'){\r\n                v.animated = true;\r\n            }\r\n\r\n            if(typeof v.anchorLink !== 'undefined'){\r\n                setURLHash(v.anchorLink, v.sectionIndex);\r\n            }\r\n\r\n            v.destination.addClass('active').siblings().removeClass('active');\r\n\r\n            v.sectionsToMove = getSectionsToMove(v);\r\n\r\n            //scrolling down (moving sections up making them disappear)\r\n            if (v.yMovement === 'down') {\r\n                v.translate3d = getTranslate3d();\r\n                v.scrolling = '-100%';\r\n\r\n                if(!options.css3){\r\n                    v.sectionsToMove.each(function(index){\r\n                        if(index != v.activeSection.index('.pp-section')){\r\n                            $(this).css(getScrollProp(v.scrolling));\r\n                        }\r\n                    });\r\n                }\r\n\r\n                v.animateSection = v.activeSection;\r\n            }\r\n\r\n            //scrolling up (moving section down to the viewport)\r\n            else {\r\n                v.translate3d = 'translate3d(0px, 0px, 0px)';\r\n                v.scrolling = '0';\r\n\r\n                v.animateSection = destination;\r\n            }\r\n\r\n            $.isFunction(options.onLeave) && options.onLeave.call(this, v.leavingSection, (v.sectionIndex + 1), v.yMovement);\r\n\r\n            performMovement(v);\r\n\r\n            activateMenuElement(v.anchorLink);\r\n            activateNavDots(v.anchorLink, v.sectionIndex);\r\n            lastScrolledDestiny = v.anchorLink;\r\n\r\n            var timeNow = new Date().getTime();\r\n            lastAnimation = timeNow;\r\n        }\r\n\r\n        /**\r\n        * Performs the movement (by CSS3 or by jQuery)\r\n        */\r\n        function performMovement(v){\r\n            if(options.css3){\r\n                transformContainer(v.animateSection, v.translate3d, v.animated);\r\n\r\n                v.sectionsToMove.each(function(){\r\n                    transformContainer($(this), v.translate3d, v.animated);\r\n                });\r\n\r\n                setTimeout(function () {\r\n                    afterSectionLoads(v);\r\n                }, options.scrollingSpeed);\r\n            }else{\r\n                v.scrollOptions = getScrollProp(v.scrolling);\r\n\r\n                if(v.animated){\r\n                    v.animateSection.animate(\r\n                        v.scrollOptions,\r\n                    options.scrollingSpeed, options.easing, function () {\r\n                        readjustSections(v);\r\n                        afterSectionLoads(v);\r\n                    });\r\n                }else{\r\n                    v.animateSection.css(getScrollProp(v.scrolling));\r\n                    setTimeout(function(){\r\n                        readjustSections(v);\r\n                        afterSectionLoads(v);\r\n                    },400);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Actions to execute after a secion is loaded\r\n        */\r\n        function afterSectionLoads(v){\r\n            //callback (afterLoad) if the site is not just resizing and readjusting the slides\r\n            $.isFunction(options.afterLoad) && options.afterLoad.call(this, v.anchorLink, (v.sectionIndex + 1));\r\n        }\r\n\r\n\r\n        function getSectionsToMove(v){\r\n            var sectionToMove;\r\n\r\n            if(v.yMovement === 'down'){\r\n                sectionToMove = $('.pp-section').map(function(index){\r\n                    if (index < v.destination.index('.pp-section')){\r\n                        return $(this);\r\n                    }\r\n                });\r\n            }else{\r\n                sectionToMove = $('.pp-section').map(function(index){\r\n                    if (index > v.destination.index('.pp-section')){\r\n                        return $(this);\r\n                    }\r\n                });\r\n            }\r\n\r\n            return sectionToMove;\r\n        }\r\n\r\n        /**\r\n        * Returns the sections to re-adjust in the background after the section loads.\r\n        */\r\n        function readjustSections(v){\r\n            if(v.yMovement === 'up'){\r\n                v.sectionsToMove.each(function(index){\r\n                    $(this).css(getScrollProp(v.scrolling));\r\n                });\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets the property used to create the scrolling effect when using jQuery animations\r\n        * depending on the plugin direction option.\r\n        */\r\n        function getScrollProp(propertyValue){\r\n            if(options.direction === 'vertical'){\r\n                return {'top': propertyValue};\r\n            }\r\n            return {'left': propertyValue};\r\n        }\r\n\r\n        /**\r\n        * Scrolls the site without anymations (usually used in the background without the user noticing it)\r\n        */\r\n        function silentScroll(section, offset){\r\n            if (options.css3) {\r\n                transformContainer(section, getTranslate3d(), false);\r\n            }\r\n            else{\r\n                section.css(getScrollProp(offset));\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Sets the URL hash for a section with slides\r\n        */\r\n        function setURLHash(anchorLink, sectionIndex){\r\n            if(options.anchors.length){\r\n                location.hash = anchorLink;\r\n\r\n                setBodyClass(location.hash);\r\n            }else{\r\n                setBodyClass(String(sectionIndex));\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Sets a class for the body of the page depending on the active section / slide\r\n        */\r\n        function setBodyClass(text){\r\n            //removing the #\r\n            text = text.replace('#','');\r\n\r\n            //removing previous anchor classes\r\n            $('body')[0].className = $('body')[0].className.replace(/\\b\\s?pp-viewing-[^\\s]+\\b/g, '');\r\n\r\n            //adding the current anchor\r\n            $('body').addClass('pp-viewing-' + text);\r\n        }\r\n\r\n        //TO DO\r\n        function scrollToAnchor(){\r\n            //getting the anchor link in the URL and deleting the `#`\r\n            var value =  window.location.hash.replace('#', '');\r\n            var sectionAnchor = value;\r\n            var section = $(document).find('.pp-section[data-anchor=\"'+sectionAnchor+'\"]');\r\n\r\n            if(section.length > 0){  //if theres any #\r\n                scrollPage(section, options.animateAnchor);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Determines if the transitions between sections still taking place.\r\n        * The variable `scrollDelay` adds a \"save zone\" for devices such as Apple laptops and Apple magic mouses\r\n        */\r\n        function isMoving(){\r\n            var timeNow = new Date().getTime();\r\n            // Cancel scroll if currently animating or within quiet period\r\n            if (timeNow - lastAnimation < scrollDelay + options.scrollingSpeed) {\r\n                return true;\r\n            }\r\n            return false;\r\n        }\r\n\r\n        //detecting any change on the URL to scroll to the given anchor link\r\n        //(a way to detect back history button as we play with the hashes on the URL)\r\n        $(window).on('hashchange', hashChangeHandler);\r\n\r\n        /**\r\n        * Actions to do when the hash (#) in the URL changes.\r\n        */\r\n        function hashChangeHandler(){\r\n            var value =  window.location.hash.replace('#', '').split('/');\r\n            var sectionAnchor = value[0];\r\n\r\n            if(sectionAnchor.length){\r\n                /*in order to call scrollpage() only once for each destination at a time\r\n                It is called twice for each scroll otherwise, as in case of using anchorlinks `hashChange`\r\n                event is fired on every scroll too.*/\r\n                if (sectionAnchor && sectionAnchor !== lastScrolledDestiny)  {\r\n                    var section;\r\n\r\n                    if(isNaN(sectionAnchor)){\r\n                        section = $(document).find('[data-anchor=\"'+sectionAnchor+'\"]');\r\n                    }else{\r\n                        section = $('.pp-section').eq( (sectionAnchor -1) );\r\n                    }\r\n                    scrollPage(section);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Cross browser transformations\r\n        */\r\n        function getTransforms(translate3d) {\r\n            return {\r\n                '-webkit-transform': translate3d,\r\n                    '-moz-transform': translate3d,\r\n                    '-ms-transform': translate3d,\r\n                    'transform': translate3d\r\n            };\r\n        }\r\n\r\n        /**\r\n         * Adds a css3 transform property to the container class with or without animation depending on the animated param.\r\n         */\r\n        function transformContainer(element, translate3d, animated) {\r\n            element.toggleClass('pp-easing', animated);\r\n\r\n            element.css(getTransforms(translate3d));\r\n        }\r\n\r\n        /**\r\n         * Sliding with arrow keys, both, vertical and horizontal\r\n         */\r\n        $(document).keydown(function (e) {\r\n            if(options.keyboardScrolling && !isMoving()){\r\n                //Moving the main page with the keyboard arrows if keyboard scrolling is enabled\r\n                switch (e.which) {\r\n                        //up\r\n                    case 38:\r\n                    case 33:\r\n                        PP.moveSectionUp();\r\n                        break;\r\n\r\n                        //down\r\n                    case 40:\r\n                    case 34:\r\n                        PP.moveSectionDown();\r\n                        break;\r\n\r\n                        //Home\r\n                    case 36:\r\n                        PP.moveTo(1);\r\n                        break;\r\n\r\n                        //End\r\n                    case 35:\r\n                        PP.moveTo($('.pp-section').length);\r\n                        break;\r\n\r\n                        //left\r\n                    case 37:\r\n                        PP.moveSectionUp();\r\n                        break;\r\n\r\n                        //right\r\n                    case 39:\r\n                        PP.moveSectionDown();\r\n                        break;\r\n\r\n                    default:\r\n                        return; // exit this handler for other keys\r\n                }\r\n            }\r\n        });\r\n\r\n        /**\r\n        * If `normalScrollElements` is used, the mouse wheel scrolling will scroll normally\r\n        * over the defined elements in the option.\r\n        */\r\n        if(options.normalScrollElements){\r\n            $(document).on('mouseenter', options.normalScrollElements, function () {\r\n                PP.setMouseWheelScrolling(false);\r\n            });\r\n\r\n            $(document).on('mouseleave', options.normalScrollElements, function(){\r\n                PP.setMouseWheelScrolling(true);\r\n            });\r\n        }\r\n\r\n        /**\r\n         * Detecting mousewheel scrolling\r\n         *\r\n         * http://blogs.sitepointstatic.com/examples/tech/mouse-wheel/index.html\r\n         * http://www.sitepoint.com/html5-javascript-mouse-wheel/\r\n         */\r\n        var prevTime = new Date().getTime();\r\n\r\n        function MouseWheelHandler(e) {\r\n        \tvar curTime = new Date().getTime();\r\n\r\n        \t// cross-browser wheel delta\r\n            e = e || window.event;\r\n            var value = e.wheelDelta || -e.deltaY || -e.detail;\r\n            var delta = Math.max(-1, Math.min(1, value));\r\n\r\n            var horizontalDetection = typeof e.wheelDeltaX !== 'undefined' || typeof e.deltaX !== 'undefined';\r\n            var isScrollingVertically = (Math.abs(e.wheelDeltaX) < Math.abs(e.wheelDelta)) || (Math.abs(e.deltaX ) < Math.abs(e.deltaY) || !horizontalDetection);\r\n\r\n\t\t\t//Limiting the array to 150 (lets not waste memory!)\r\n            if(scrollings.length > 149){\r\n                scrollings.shift();\r\n            }\r\n\r\n            //keeping record of the previous scrollings\r\n            scrollings.push(Math.abs(value));\r\n\r\n            //time difference between the last scroll and the current one\r\n            var timeDiff = curTime-prevTime;\r\n            prevTime = curTime;\r\n\r\n            //haven't they scrolled in a while?\r\n            //(enough to be consider a different scrolling action to scroll another section)\r\n            if(timeDiff > 200){\r\n                //emptying the array, we dont care about old scrollings for our averages\r\n                scrollings = [];\r\n            }\r\n\r\n            if(!isMoving()){\r\n                var activeSection = $('.pp-section.active');\r\n                var scrollable = isScrollable(activeSection);\r\n\r\n                var averageEnd = getAverage(scrollings, 10);\r\n                var averageMiddle = getAverage(scrollings, 70);\r\n                var isAccelerating = averageEnd >= averageMiddle;\r\n\r\n                if(isAccelerating && isScrollingVertically){\r\n\t                //scrolling down?\r\n\t                if (delta < 0) {\r\n\t                    scrolling('down', scrollable);\r\n\r\n\t                //scrolling up?\r\n\t                }else if(delta>0){\r\n\t                    scrolling('up', scrollable);\r\n\t                }\r\n\t            }\r\n\r\n                return false;\r\n            }\r\n         }\r\n\r\n        /**\r\n        * Gets the average of the last `number` elements of the given array.\r\n        */\r\n        function getAverage(elements, number){\r\n            var sum = 0;\r\n\r\n            //taking `number` elements from the end to make the average, if there are not enought, 1\r\n            var lastElements = elements.slice(Math.max(elements.length - number, 1));\r\n\r\n            for(var i = 0; i < lastElements.length; i++){\r\n                sum = sum + lastElements[i];\r\n            }\r\n\r\n            return Math.ceil(sum/number);\r\n        }\r\n\r\n        /**\r\n        * Determines the way of scrolling up or down:\r\n        * by 'automatically' scrolling a section or by using the default and normal scrolling.\r\n        */\r\n        function scrolling(type, scrollable){\r\n            var check;\r\n            var scrollSection;\r\n\r\n            if(type == 'down'){\r\n                check = 'bottom';\r\n                scrollSection = PP.moveSectionDown;\r\n            }else{\r\n                check = 'top';\r\n                scrollSection = PP.moveSectionUp;\r\n            }\r\n\r\n            if(scrollable.length > 0 ){\r\n                //is the scrollbar at the start/end of the scroll?\r\n                if(isScrolled(check, scrollable)){\r\n                    scrollSection();\r\n                }else{\r\n                    return true;\r\n                }\r\n            }else{\r\n                //moved up/down\r\n                scrollSection();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Return a boolean depending on whether the scrollable element is at the end or at the start of the scrolling\r\n        * depending on the given type.\r\n        */\r\n        function isScrolled(type, scrollable){\r\n            if(type === 'top'){\r\n                return !scrollable.scrollTop();\r\n            }else if(type === 'bottom'){\r\n                return scrollable.scrollTop() + 1 + scrollable.innerHeight() >= scrollable[0].scrollHeight;\r\n            }\r\n        }\r\n\r\n         /**\r\n        * Determines whether the active section or slide is scrollable through and scrolling bar\r\n        */\r\n        function isScrollable(activeSection){\r\n            return activeSection.filter('.pp-scrollable');\r\n        }\r\n\r\n        /**\r\n        * Removes the auto scrolling action fired by the mouse wheel and tackpad.\r\n        * After this function is called, the mousewheel and trackpad movements won't scroll through sections.\r\n        */\r\n        function removeMouseWheelHandler(){\r\n            if (container.get(0).addEventListener) {\r\n                container.get(0).removeEventListener('mousewheel', MouseWheelHandler, false); //IE9, Chrome, Safari, Oper\r\n                container.get(0).removeEventListener('wheel', MouseWheelHandler, false); //Firefox\r\n            } else {\r\n                container.get(0).detachEvent('onmousewheel', MouseWheelHandler); //IE 6/7/8\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds the auto scrolling action for the mouse wheel and tackpad.\r\n        * After this function is called, the mousewheel and trackpad movements will scroll through sections\r\n        */\r\n        function addMouseWheelHandler(){\r\n            if (container.length) {\r\n                if (container.get(0).addEventListener) {\r\n                    container.get(0).addEventListener('mousewheel', MouseWheelHandler, false); //IE9, Chrome, Safari, Oper\r\n                    container.get(0).addEventListener('wheel', MouseWheelHandler, false); //Firefox\r\n                } else {\r\n                    container.get(0).attachEvent('onmousewheel', MouseWheelHandler); //IE 6/7/8\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds the possibility to auto scroll through sections on touch devices.\r\n        */\r\n        function addTouchHandler(){\r\n            if(isTouch){\r\n                //Microsoft pointers\r\n                var MSPointer = getMSPointer();\r\n\r\n                container.off('touchstart ' +  MSPointer.down).on('touchstart ' + MSPointer.down, touchStartHandler);\r\n                container.off('touchmove ' + MSPointer.move).on('touchmove ' + MSPointer.move, touchMoveHandler);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Removes the auto scrolling for touch devices.\r\n        */\r\n        function removeTouchHandler(){\r\n            if(isTouch){\r\n                //Microsoft pointers\r\n                var MSPointer = getMSPointer();\r\n\r\n                container.off('touchstart ' + MSPointer.down);\r\n                container.off('touchmove ' + MSPointer.move);\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Returns and object with Microsoft pointers (for IE<11 and for IE >= 11)\r\n        * http://msdn.microsoft.com/en-us/library/ie/dn304886(v=vs.85).aspx\r\n        */\r\n        function getMSPointer(){\r\n            var pointer;\r\n\r\n            //IE >= 11 & rest of browsers\r\n            if(window.PointerEvent){\r\n                pointer = { down: 'pointerdown', move: 'pointermove', up: 'pointerup'};\r\n            }\r\n\r\n            //IE < 11\r\n            else{\r\n                pointer = { down: 'MSPointerDown', move: 'MSPointerMove', up: 'MSPointerUp'};\r\n            }\r\n\r\n            return pointer;\r\n        }\r\n\r\n        /**\r\n        * Gets the pageX and pageY properties depending on the browser.\r\n        * https://github.com/alvarotrigo/fullPage.js/issues/194#issuecomment-34069854\r\n        */\r\n        function getEventsPage(e){\r\n            var events = new Array();\r\n\r\n            events.y = (typeof e.pageY !== 'undefined' && (e.pageY || e.pageX) ? e.pageY : e.touches[0].pageY);\r\n            events.x = (typeof e.pageX !== 'undefined' && (e.pageY || e.pageX) ? e.pageX : e.touches[0].pageX);\r\n\r\n            return events;\r\n        }\r\n\r\n        /**\r\n        * As IE >= 10 fires both touch and mouse events when using a mouse in a touchscreen\r\n        * this way we make sure that is really a touch event what IE is detecting.\r\n        */\r\n        function isReallyTouch(e){\r\n            //if is not IE   ||  IE is detecting `touch` or `pen`\r\n            return typeof e.pointerType === 'undefined' || e.pointerType != 'mouse';\r\n        }\r\n\r\n        /**\r\n        * Getting the starting possitions of the touch event\r\n        */\r\n        function touchStartHandler(event){\r\n            var e = event.originalEvent;\r\n\r\n            if(isReallyTouch(e)){\r\n                var touchEvents = getEventsPage(e);\r\n                touchStartY = touchEvents.y;\r\n                touchStartX = touchEvents.x;\r\n            }\r\n        }\r\n\r\n        /* Detecting touch events\r\n        */\r\n        function touchMoveHandler(event){\r\n            var e = event.originalEvent;\r\n\r\n            // additional: if one of the normalScrollElements isn't within options.normalScrollElementTouchThreshold hops up the DOM chain\r\n            if ( !checkParentForNormalScrollElement(event.target) && isReallyTouch(e) ) {\r\n\r\n                var activeSection = $('.pp-section.active');\r\n                var scrollable = isScrollable(activeSection);\r\n\r\n                if(!scrollable.length){\r\n                    event.preventDefault();\r\n                }\r\n\r\n                if (!isMoving()) {\r\n                    var touchEvents = getEventsPage(e);\r\n                    touchEndY = touchEvents.y;\r\n                    touchEndX = touchEvents.x;\r\n\r\n                  //$('body').append('<span style=\"position:fixed; top: 250px; left: 20px; z-index:88; font-size: 25px; color: #000;\">touchEndY: ' + touchEndY  + '</div>');\r\n\r\n                    //X movement bigger than Y movement?\r\n                    if (options.direction === 'horizontal' && Math.abs(touchStartX - touchEndX) > (Math.abs(touchStartY - touchEndY))) {\r\n                        //is the movement greater than the minimum resistance to scroll?\r\n                        if (Math.abs(touchStartX - touchEndX) > (container.width() / 100 * options.touchSensitivity)) {\r\n                            if (touchStartX > touchEndX) {\r\n                                scrolling('down', scrollable);\r\n                            } else if (touchEndX > touchStartX) {\r\n                                scrolling('up', scrollable);\r\n                            }\r\n                        }\r\n                    } else {\r\n                        if (Math.abs(touchStartY - touchEndY) > (container.height() / 100 * options.touchSensitivity)) {\r\n                            if (touchStartY > touchEndY) {\r\n                                scrolling('down', scrollable);\r\n                            } else if (touchEndY > touchStartY) {\r\n                                scrolling('up', scrollable);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n         * recursive function to loop up the parent nodes to check if one of them exists in options.normalScrollElements\r\n         * Currently works well for iOS - Android might need some testing\r\n         * @param  {Element} el  target element / jquery selector (in subsequent nodes)\r\n         * @param  {int}     hop current hop compared to options.normalScrollElementTouchThreshold\r\n         * @return {boolean} true if there is a match to options.normalScrollElements\r\n         */\r\n        function checkParentForNormalScrollElement (el, hop) {\r\n            hop = hop || 0;\r\n            var parent = $(el).parent();\r\n\r\n            if (hop < options.normalScrollElementTouchThreshold &&\r\n                parent.is(options.normalScrollElements) ) {\r\n                return true;\r\n            } else if (hop == options.normalScrollElementTouchThreshold) {\r\n                return false;\r\n            } else {\r\n                return checkParentForNormalScrollElement(parent, ++hop);\r\n            }\r\n        }\r\n\r\n\r\n        /**\r\n        * Creates a vertical navigation bar.\r\n        */\r\n        function addVerticalNavigation(){\r\n            $('body').append('<div id=\"pp-nav\"><ul></ul></div>');\r\n            var nav = $('#pp-nav');\r\n\r\n            nav.css('color', options.navigation.textColor);\r\n\r\n            nav.addClass(options.navigation.position);\r\n\r\n            for(var cont = 0; cont < $('.pp-section').length; cont++){\r\n                var link = '';\r\n                if(options.anchors.length){\r\n                    link = options.anchors[cont];\r\n                }\r\n                if(options.navigation.tooltips !== 'undefined'){\r\n                    var tooltip = options.navigation.tooltips[cont];\r\n                    if(typeof tooltip === 'undefined'){\r\n                        tooltip = '';\r\n                    }\r\n                }\r\n\r\n                nav.find('ul').append('<li data-tooltip=\"' + tooltip + '\"><a href=\"#' + link + '\"><span></span></a></li>');\r\n            }\r\n\r\n            nav.find('span').css('border-color', options.navigation.bulletsColor);\r\n        }\r\n\r\n        /**\r\n        * Scrolls to the section when clicking the navigation bullet\r\n        */\r\n        $(document).on('click touchstart', '#pp-nav a', function(e){\r\n            e.preventDefault();\r\n            var index = $(this).parent().index();\r\n\r\n            scrollPage($('.pp-section').eq(index));\r\n        });\r\n\r\n        /**\r\n        * Navigation tooltips\r\n        */\r\n        $(document).on({\r\n            mouseenter: function(){\r\n                var tooltip = $(this).data('tooltip');\r\n                $('<div class=\"pp-tooltip ' + options.navigation.position +'\">' + tooltip + '</div>').hide().appendTo($(this)).fadeIn(200);\r\n            },\r\n            mouseleave: function(){\r\n                $(this).find('.pp-tooltip').fadeOut(200, function() {\r\n                    $(this).remove();\r\n                });\r\n            }\r\n        }, '#pp-nav li');\r\n\r\n         /**\r\n         * Activating the website navigation dots according to the given slide name.\r\n         */\r\n        function activateNavDots(name, sectionIndex){\r\n            if(options.navigation){\r\n                $('#pp-nav').find('.active').removeClass('active');\r\n                if(name){\r\n                    $('#pp-nav').find('a[href=\"#' + name + '\"]').addClass('active');\r\n                }else{\r\n                    $('#pp-nav').find('li').eq(sectionIndex).find('a').addClass('active');\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n         * Activating the website main menu elements according to the given slide name.\r\n         */\r\n        function activateMenuElement(name){\r\n            if(options.menu){\r\n                $(options.menu).find('.active').removeClass('active');\r\n                $(options.menu).find('[data-menuanchor=\"'+name+'\"]').addClass('active');\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Checks for translate3d support\r\n        * @return boolean\r\n        * http://stackoverflow.com/questions/5661671/detecting-transform-translate3d-support\r\n        */\r\n        function support3d() {\r\n            var el = document.createElement('p'),\r\n                has3d,\r\n                transforms = {\r\n                    'webkitTransform':'-webkit-transform',\r\n                    'OTransform':'-o-transform',\r\n                    'msTransform':'-ms-transform',\r\n                    'MozTransform':'-moz-transform',\r\n                    'transform':'transform'\r\n                };\r\n\r\n            // Add it to the body to get the computed style.\r\n            document.body.insertBefore(el, null);\r\n\r\n            for (var t in transforms) {\r\n                if (el.style[t] !== undefined) {\r\n                    el.style[t] = 'translate3d(1px,1px,1px)';\r\n                    has3d = window.getComputedStyle(el).getPropertyValue(transforms[t]);\r\n                }\r\n            }\r\n\r\n            document.body.removeChild(el);\r\n\r\n            return (has3d !== undefined && has3d.length > 0 && has3d !== 'none');\r\n        }\r\n\r\n        /**\r\n        * Gets the translate3d property to apply when using css3:true depending on the `direction` option.\r\n        */\r\n        function getTranslate3d(){\r\n            if (options.direction !== 'vertical') {\r\n                  return 'translate3d(-100%, 0px, 0px)';\r\n            }\r\n\r\n            return 'translate3d(0px, -100%, 0px)';\r\n        }\r\n\r\n    };\r\n})(jQuery, document, window);"]}