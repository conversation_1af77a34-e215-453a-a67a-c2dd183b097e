<!doctype html>
<html lang="en">
  <head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/prism.css">
    <title>Liarch - Architecture & Interior HTML Template</title>
  </head>
  <body data-spy="scroll" data-target="#navbar-example">
  	<header class="header">
	<div class="container">
		<h2>Liarch - Architecture & Interior HTML Template</h2>
		<p class="text-muted">Design to attract, convert and<br>
delight your customers</p>
	</div>
	</header>
  	<div class="container-fluid">
	  	<div class="row">
	  		<nav id="navbar-example" class="col-12 col-md-3 col-xl-2 sidebar py-5">
			  	<ul class="nav d-block">
				  <li><a  href="#project">Project Structure</a>
				  </li>
				  <li><a  href="#slider">Revolution slider</a>
				  </li>
				  <li><a  href="#number">Number counter</a>
				  </li>
				  <li><a  href="#carousel">Swiper Slider</a>
				  </li>
				   <li><a  href="#piling">Home Parallax Piling</a>
				  </li>
	
				  <li><a  href="#filters">Filters</a>
				  </li>
				  <li><a  href="#minimal">Modals</a>
				  </li>
				</ul>
			</nav>
		    <div class="col-12 col-md-9 col-xl-8 py-5 pl-md-5 content">
			   	<div id="project">
			   		<h2 class="mb-5">Project Structure</h2>
			   		<div class="alert alert-danger" role="alert">Liarch will work correctly only on the web server!</div>
				   	<p>The structure of the Liarch HTML template:</p>
			        <ul>
		              <li><b>css</b> - all common scripts used on the pages, theme styles included in <b>theme.css</b>, <b>responsive.css</b> and <b>dark.css</b> files</li>
			          <li><b>favicons</b> - all favicons, favicons were generated here <a href="https://realfavicongenerator.net/">https://realfavicongenerator.net/</a></li>
		              <li><b>fonts</b> - all fonts used on the pages</li>
		              <li><b>img</b> - images, icons and backgrounds for the project</li>
		              <li><b>js</b> - all common  scripts used on the pages file</li>
			          <li><b>php</b> - the script for the site forms</li>
			          <li><b>video</b> - all videos for the site</li>
			        </ul>
			        <h5 class="my-4">Head</h5>
			        <img alt="" src="img/head.jpg">
				
		   		</div>
		   		<div id="slider" class="my-5">
		   			<h3>Revolution Slider</h3>
		   			<p>All Revolution sliders are built with plugin <a href="https://revolution.themepunch.com/jquery/">Revolution jQuery Slider</a>, you can find documentation <a href="https://www.themepunch.com/revsliderjquery-doc/slider-revolution-jquery-5-x-documentation/">here</a></p>
		   			<p><img src="img/revolution.jpg"></p>
		   			<p>
		   				The slider consists of layers, in them you can set the position of the elements using <b>data-x</b>, <b>data-y</b>, <b>data-hoffset</b>, <b>data-voffset</b>
		   			</p>
		   			<p><img src="img/revolution2.jpg"></p>
		   			<p>
		   				The main image of the each slide is in the folder <b>img</b>
		   				<p><img src="img/revolution3.jpg"></p>
		   				Also full documentation you can find  <a href="https://www.themepunch.com/revsliderjquery-doc/slider-revolution-jquery-5-x-documentation/">here</a>
		   			</p>
		   		</div>
		   		<div id="number" class="my-5">
		   			<h3 class="mb-4">Number Counter</h3>
		   			<p class="mb-5"><img src="img/counter.jpg"></p>
					<h5 class="my-4"><b>To change numbers, change this values:</b></h5>
					<p><img src="img/counter2.jpg"></p>
		   			
		   		</div>
		   		<div id="carousel" class="my-5">
		   			<h3 class="mb-4">Swiper Slider</h3>
		   			<p> Swiper slider works with <a href="http://www.idangero.us/swiper/">Swiper jQuery plugin.</a> It's easy to customize, see documentation
		   			<p><img src="img/swiper.jpg"></p>
		   			!! !! !! !! !!
		   			<h5 class="my-4"><b>Change filter project</b></h5>
		   			<p>Change <b>data-value</b> here:
		   			<p><img src="img/filter.png"></p>
		   			<p>Then change same name in <b>data-filter</b> here:
		   			<p><img src="img/filter2.png"></p>

		   		</div>

		   		<div id="piling" class="my-5">
		   			<h3 class="mb-4">Home Parallax Piling</h3>
		   			<p>Home parallax piling works with <a href="https://alvarotrigo.com/pagePiling/">jQuery PagePiling plugin</a>
		   			<p><img src="img/page-pilling.jpg"></p>
		   			<p>To make white text in section add class <b>dark</b></p>
		   				<p><img src="img/page-pilling2.jpg"></p>
		   		</div>
		   		
		   		<div id="filters" class="my-5">
		   			<h5 class="mt-5">Filters:</h5>
		   			<p><img src="img/filter.jpg"></p>
		   			<p>Each filter has a data attribute with a class (see line 790-793 in the screenshot). Each element has the same classes (see line 798 and 810 in the screenshot)</p>
		   			<p><img src="img/filter2.jpg"></p>
		   		</div>
		   		<div id="modals" class="my-5">
		   			<h5 class="mt-5">Modal</h5>
		   			
		   			<p>Modals work with the fancybox plugin It is easy to configure, see the <a href="http://fancybox.net/" target="_blank">documentation</a></p>
	
		   		</div>
			</div>
		</div>
	</div>
    <!-- Optional JavaScript -->
    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="js/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
    <script  src="js/prism.js"></script>
    <script  src="js/script.js"></script> 
  </body>
</html>