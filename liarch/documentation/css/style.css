body{
    position: relative; 
}

a{
    color:#f14201;
}

img{
    max-width: 100%;
}
.header{
    background: url(../img/QQQ_QQQ/header.jpg) center bottom;
    padding: 100px 0;
    background-size:cover;
}

.nav li a {
    display: block;
    padding: .25rem 1.5rem;
    font-weight: 600;
    color: rgba(0,0,0,.65);
}
.nav li .active,
.nav li a:hover{ color:#f14201; text-decoration:none;}

.nav li ul li a{
    font-size: 90%;
}

.sidebar {
    -ms-flex: 0 1 320px;
    flex: 0 1 320px;
}
@media (min-width: 768px){
    .sidebar {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1000;
        height: calc(100vh - 4rem);
        border-right: 1px solid rgba(0,0,0,.1);
    }
}

.sidebar {
    -ms-flex-order: 0;
    order: 0;
    border-bottom: 1px solid rgba(0,0,0,.1);
}

.scrollspy-example {
    position: relative;

    margin-top: .5rem;
    overflow: auto;
}